name: Destroy infrastructure
run-name: Destroy infrastructure for ${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to be Destroyed"
        required: true
        type: choice
        options:
          - qa
          - test
          - preview
          - training
          - sandbox-alpha
          - sandbox-beta

env:
  aws_role: arn:aws:iam::393416225559:role/GithubDeployMavisAndInfrastructure

permissions: {}

jobs:
  destroy-resources:
    name: Destroy resources
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    environment: ${{ inputs.environment }}
    defaults:
      run:
        working-directory: terraform/app
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.aws_role }}
          aws-region: eu-west-2
      - name: Install terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.10.5
      - name: Ensure DB cluster can be deleted
        run: |
          set -e
          terraform init -backend-config="env/${{ inputs.environment }}-backend.hcl" -upgrade
          if terraform state list | grep -q 'aws_rds_cluster.aurora_cluster'; then
            echo "DB cluster exsits: removing delete protection"
            CLUSTER_IDENTIFIER=$(grep -oP 'db_cluster\s*=\s*"\K[^"]+' env/${{ inputs.environment }}.tfvars)
            aws rds modify-db-cluster --db-cluster-identifier "$CLUSTER_IDENTIFIER" --no-deletion-protection
            echo "DB cluster delete protection removed: proceeding to delete stage"
          else
            echo "DB cluster not in state: proceeding to delete stage"
          fi

      - name: Delete cluster
        run: |
          set -e
          terraform destroy -var-file="env/${{ inputs.environment }}.tfvars" \
          -var="image_digest=notneededfordestroy" -auto-approve

  destroy-backend:
    name: Destroy backend
    runs-on: ubuntu-latest
    needs: destroy-resources
    permissions:
      id-token: write
    environment: ${{ inputs.environment }}
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.aws_role }}
          aws-region: eu-west-2
      - name: Install AWS CLI
        run: |
          sudo snap install --classic aws-cli
      - name: Delete terraform backend elements
        run: |
          set -e
          TF_STATE_FILE=nhse-mavis-terraform-state/terraform-${{ inputs.environment }}.tfstate
          aws s3 rm s3://$TF_STATE_FILE
          aws dynamodb delete-item --table-name mavis-terraform-state-lock \
          --key "{\"LockID\": {\"S\": \"$TF_STATE_FILE-md5\"}}"
