name: Deploy backup vault infrastructure
run-name: Deploy backup vault infrastructure for ${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: Deployment environment
        required: true
        type: choice
        options:
          - development
          - production

permissions: {}

concurrency:
  group: backup-infrastructure-${{ inputs.environment }}

env:
  aws_role: ${{ inputs.environment == 'production'
    && 'arn:aws:iam::820242920762:role/GithubDeployMavisAndInfrastructure'
    || 'arn:aws:iam::393416225559:role/GithubDeployMavisAndInfrastructure' }}

defaults:
  run:
    working-directory: terraform/backup/source

jobs:
  plan:
    name: Terraform plan
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.aws_role }}
          aws-region: eu-west-2
      - name: Install terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.10.5
      - name: Terraform Plan
        id: plan
        env:
          PERSONAL_ACCESS_TOKEN: ${{ secrets.BACKUP_MODULES_ACCESS_TOKEN }}
        run: |
          set -e
          git config --global url."https://foo:${PERSONAL_ACCESS_TOKEN}@github.com/NHSDigital".insteadOf "https://github.com/NHSDigital"
          terraform init -backend-config="env/${{ inputs.environment }}-backend.hcl" -upgrade
          terraform plan -var-file="env/${{ inputs.environment }}.tfvars" \
          -out ${{ runner.temp }}/tfplan | tee ${{ runner.temp }}/tf_stdout
      - name: Validate the changes
        run: |
          set -e
          ../../scripts/validate_plan.sh ${{ runner.temp }}/tf_stdout
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: tfplan_infrastructure-${{ inputs.environment }}
          path: ${{ runner.temp }}/tfplan

  apply:
    name: Terraform apply
    runs-on: ubuntu-latest
    needs: plan
    environment: ${{ inputs.environment }}
    permissions:
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.aws_role }}
          aws-region: eu-west-2
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: tfplan_infrastructure-${{ inputs.environment }}
          path: ${{ runner.temp }}
      - name: Install terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.10.5
      - name: Apply the changes
        env:
          PERSONAL_ACCESS_TOKEN: ${{ secrets.BACKUP_MODULES_ACCESS_TOKEN }}
        run: |
          set -e
          git config --global url."https://foo:${PERSONAL_ACCESS_TOKEN}@github.com/NHSDigital".insteadOf "https://github.com/NHSDigital"
          terraform init -backend-config="env/${{ inputs.environment }}-backend.hcl" -upgrade
          terraform apply ${{ runner.temp }}/tfplan
