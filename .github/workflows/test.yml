name: Test

on:
  pull_request:
  workflow_call:

permissions: {}

jobs:
  pre-rails-tests:
    name: Check if Rails tests need to run
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Determine if rails tests are needed
        id: check-rails-relevant-changes
        run: |
          if [ ${{ github.event_name }} == "pull_request" ]; then
            git fetch origin || { echo "Fetch failed, running all tests"; \
            echo "RUN_RAILS_TESTS=true" > $GITHUB_OUTPUT; exit 0; };
            RELEVANT_DIRS=$(git diff ${{ github.event.pull_request.base.sha }} origin/${{ github.event.pull_request.head.ref }}  --name-only)
            if [ -z "$(echo "$RELEVANT_DIRS" | egrep -v "^(.github|adr|aws|docs|storage|terraform|tests|tests-examples)/")" ]; then
              echo "No relevant changes detected, skipping rails tests"
              echo "RUN_RAILS_TESTS=false" >> $GITHUB_OUTPUT
              exit 0
            fi
          fi
          echo "Not a pull request or detected relevant changes: Running rails tests"
          echo "RUN_RAILS_TESTS=true" >> $GITHUB_OUTPUT
    outputs:
      run-rails-tests: ${{ steps.check-rails-relevant-changes.outputs.RUN_RAILS_TESTS }}

  rails:
    name: Rails
    runs-on: ubuntu-latest
    needs: pre-rails-tests
    if: needs.pre-rails-tests.outputs.run-rails-tests == 'true'
    services:
      postgres:
        image: postgres:17.2
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    env:
      DATABASE_URL: postgres://postgres:postgres@localhost:5432/manage_vaccinations_test
      RAILS_ENV: test
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: .tool-versions
          cache: yarn
      - uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Setup database
        run: bin/rails db:test:prepare
      - name: Precompile assets
        run: bin/rails assets:precompile
      - name: Run rspec
        run: bundle exec rspec spec

  jest:
    name: Jest
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: .tool-versions
          cache: yarn
      - run: yarn install --immutable --immutable-cache --check-cache
      - run: yarn test

  playwright:
    timeout-minutes: 60
    name: Playwright
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:17.2
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    env:
      DATABASE_URL: postgres://postgres:postgres@localhost:5432/manage_vaccinations_test
      RAILS_ENV: test
      MAVIS__CIS2__ENABLED: false
    steps:
      - uses: actions/checkout@v4
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: .tool-versions
          bundler-cache: true
      - name: Get Node.js version
        id: mise
        run: echo "::set-output name=VERSION::$(grep nodejs .tool-versions | awk '{print $2}')"
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ steps.mise.outputs.VERSION }}
          cache: "yarn"
      - run: bundle
      - run: yarn install --frozen-lockfile
      - run: bin/rails db:prepare
      - name: Get installed Playwright version
        id: playwright-version
        run: echo "PLAYWRIGHT_VERSION=$(yarn list --pattern '@playwright/test' --depth 0 -s | cut -d @ -f 3)" >> $GITHUB_ENV
      - name: Cache playwright binaries
        uses: actions/cache@v4
        id: playwright-cache
        with:
          path: |
            ~/.cache/ms-playwright
          key: ${{ runner.os }}-playwright-${{ env.PLAYWRIGHT_VERSION }}
      - name: Install Playwright Browsers
        run: yarn playwright install --with-deps
        if: steps.playwright-cache.outputs.cache-hit != 'true'
      - name: Precompile assets
        run: bin/rails assets:precompile
      - name: Run Playwright tests
        run: yarn test:e2e
      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: playwright?report
          retention-days: 10

  terraform:
    name: Terraform Validate
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: terraform
    steps:
      - uses: actions/checkout@v4
      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.10.5
      - run: terraform init -backend=false
      - run: terraform validate
