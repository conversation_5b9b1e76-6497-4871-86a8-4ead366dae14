name: Deploy application
run-name: Deploy application to ${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: Deployment environment
        required: true
        type: choice
        options:
          - qa
          - test
          - preview
          - training
          - production
          - sandbox-alpha
          - sandbox-beta
      server_types:
        description: Server types to deploy
        required: true
        type: choice
        options:
          - all
          - web
          - good-job
        default: all
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      server_types:
        required: true
        type: string
      git_sha_to_deploy:
        description: The git commit SHA to deploy.
        required: true
        type: string

permissions: {}

concurrency:
  group: deploy-application-${{ inputs.environment }}

env:
  aws-role: ${{ inputs.environment == 'production'
    && 'arn:aws:iam::820242920762:role/GithubDeployMavisAndInfrastructure'
    || 'arn:aws:iam::393416225559:role/GithubDeployMavisAndInfrastructure' }}

jobs:
  prepare-deployment:
    name: Prepare deployment
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    permissions:
      id-token: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.git_sha_to_deploy || github.sha }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.aws-role }}
          aws-region: eu-west-2
      - name: Install terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.10.5
      - name: Get terraform output
        id: terraform-output
        working-directory: terraform/app
        run: |
          set -e
          terraform init -backend-config=env/${{ inputs.environment }}-backend.hcl -reconfigure
          terraform output -json | jq -r '
          "s3_bucket=" + .s3_bucket.value,
          "s3_key=" + .s3_key.value,
          "application=" + .codedeploy_application_name.value,
          "application_group=" + .codedeploy_deployment_group_name.value,
          "cluster_name=" + .ecs_variables.value.cluster_name,
          "good_job_service=" + .ecs_variables.value.good_job.service_name,
          "good_job_task_definition=" + .ecs_variables.value.good_job.task_definition.arn
          ' > ${{ runner.temp }}/DEPLOYMENT_ENVS
      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: DEPLOYMENT_ENVS-${{ inputs.environment }}
          path: ${{ runner.temp }}/DEPLOYMENT_ENVS

  create-web-deployment:
    name: Create web deployment
    runs-on: ubuntu-latest
    needs: prepare-deployment
    if: inputs.server_types == 'web' || inputs.server_types == 'all'
    permissions:
      id-token: write
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: DEPLOYMENT_ENVS-${{ inputs.environment }}
          path: ${{ runner.temp }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.aws-role }}
          aws-region: eu-west-2
      - name: Trigger CodeDeploy deployment
        run: |
          set -e
          source ${{ runner.temp }}/DEPLOYMENT_ENVS
          deployment_id=$(aws deploy create-deployment \
          --application-name "$application" --deployment-group-name "$application_group" \
          --s3-location bucket="$s3_bucket",key="$s3_key",bundleType=yaml | jq -r .deploymentId)
          echo "Deployment started: $deployment_id"
          echo "deployment_id=$deployment_id" >> $GITHUB_ENV
      - name: Wait up to 30 minutes for deployment to complete
        run: |
          set -e
          aws deploy wait deployment-successful --deployment-id "$deployment_id"
          echo "Deployment successful"

  create-good-job-deployment:
    name: Create good-job deployment
    runs-on: ubuntu-latest
    needs: prepare-deployment
    if: inputs.server_types == 'good-job' || inputs.server_types == 'all'
    permissions:
      id-token: write
    steps:
      - name: Download Artifact
        uses: actions/download-artifact@v4
        with:
          name: DEPLOYMENT_ENVS-${{ inputs.environment }}
          path: ${{ runner.temp }}
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.aws-role }}
          aws-region: eu-west-2
      - name: Install AWS CLI
        run: sudo snap install --classic aws-cli
      - name: Trigger ECS Deployment
        run: |
          set -e
          source ${{ runner.temp }}/DEPLOYMENT_ENVS
          DEPLOYMENT_ID=$(aws ecs update-service --cluster $cluster_name --service $good_job_service \
          --task-definition $good_job_task_definition --force-new-deployment \
          --query 'service.deployments[?rolloutState==`IN_PROGRESS`].[id][0]' --output text)
          echo "Deployment started: $DEPLOYMENT_ID"
          echo "deployment_id=$DEPLOYMENT_ID" >> $GITHUB_ENV
      - name: Wait for deployment to complete
        run: |
          set -e
          source ${{ runner.temp }}/DEPLOYMENT_ENVS
          DEPLOYMENT_STATE=IN_PROGRESS
          while [ "$DEPLOYMENT_STATE" == "IN_PROGRESS" ]; do
            echo "Waiting for deployment to complete..."
            sleep 30
            DEPLOYMENT_STATE="$(aws ecs describe-services --cluster $cluster_name --services $good_job_service \
            --query "services[0].deployments[?id == \`$deployment_id\`].[rolloutState][0]" --output text)"
          done
          if [ "$DEPLOYMENT_STATE" != "COMPLETED" ]; then
            echo "Deployment failed with state: $DEPLOYMENT_STATE"
            exit 1
          fi
          echo "Deployment successful"
